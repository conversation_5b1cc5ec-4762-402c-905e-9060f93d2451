import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/bloodPressure/view/blood_pressure_reading_view.dart';
import 'package:gt_plus/modules/ppg/view/ppg_reading_view.dart';
import 'package:gt_plus/modules/temprature/view/temperature_reading_view.dart';

import '../../services/prefs_service.dart';
import '../../services/remoteConfig/firebase_remote_config_service.dart';
import '../../utils/reusableWidgets/build_empty_scan_list.dart';
import '../ppg/view/ppg_waveform.dart';

enum BleDevice {
  ppg("PPG"),
  temp("Temperature"),
  bp("Blood Pressure");

  final String description;
  const BleDevice(this.description);
}

enum DeviceStatus {
  connected(text: 'Connected', color: Colors.green),
  disconnected(text: 'Disconnected', color: Colors.red),
  connecting(text: 'Connecting...', color: Colors.orange);

  final String text;
  final Color color;

  const DeviceStatus({required this.text, required this.color});
}

class BleFlutterController extends GetxController {
  final RxList<BluetoothDevice> devices = <BluetoothDevice>[].obs;
  final RxMap<String, List<Guid>> deviceAdvertisedServices =
      <String, List<Guid>>{}.obs;

  final Map<String, StreamSubscription<BluetoothConnectionState>>
  _connectionSubscriptions = {};

  // final RxSet<String> connectingDeviceIds = <String>{}.obs;
  RxBool inConnectingPopUp = false.obs;

  final RxMap<BleDevice, BluetoothDevice?> connectedDevices =
      <BleDevice, BluetoothDevice?>{
        BleDevice.ppg: null,
        BleDevice.temp: null,
        BleDevice.bp: null,
      }.obs;

  final RxBool isScanning = false.obs;
  final RxString status = 'Disconnected'.obs;
  final PrefsService prefs = PrefsService();
  final FirebaseRemoteConfigService _remoteConfigService =
  FirebaseRemoteConfigService();

  // Auto-reconnect related variables
  final RxBool attemptingReconnect = false.obs;
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;
  Timer? _reconnectTimer;
  String? lastConnectedDeviceId;
  final List<Guid> targetServiceUuids = [
    oximeterService,
    tempService,
    bpService,
  ];

  RxBool canShowSubmitButtonForPPG = false.obs;

  // Device-specific data
  final RxDouble temperature = 0.0.obs;
  final RxInt heartRate = 0.obs;
  final RxInt spo2 = 0.obs;
  final RxDouble pi = 0.0.obs;
  final RxList<LiveData> ecgData = <LiveData>[].obs;
  // New observables for blood pressure
  final RxInt systolicBP = 0.obs;
  final RxInt diastolicBP = 0.obs;

  final RxInt remainingSeconds = 0.obs;
  Timer? timer;

  int timeCounter = 0;
  String? _lastProcessedData; // To prevent duplicate data processing

  // UUIDs for existing services
  static final oximeterService = Guid('CDEACB80-5235-4C07-8846-93A37EE6B86D');
  static final oximeterCharacteristic =
  Guid('CDEACB81-5235-4C07-8846-93A37EE6B86D');
  static final tempService = Guid('0000FFF0-0000-1000-8000-00805F9B34FB');
  static final tempCharacteristic =
  Guid('0000FFF3-0000-1000-8000-00805F9B34FB');

  static final bpService = Guid('00001810-0000-1000-8000-00805F9B34FB');
  static final bpCharacteristic = Guid('00002A35-0000-1000-8000-00805F9B34FB');

  StreamSubscription<List<int>>? _notificationSubscription;

  @override
  void onInit() {
    super.onInit();
    FlutterBluePlus.adapterState.listen((state) {
      if (state == BluetoothAdapterState.on && lastConnectedDeviceId != null) {
        _attemptReconnect();
      }
    });
  }

  @override
  void onClose() {
    _notificationSubscription?.cancel();
    _connectionStateSubscription?.cancel();
    _cancelReconnectTimer();
    disconnect();
    super.onClose();
  }

  Future<void> initializeTimerValue() async {
    try {
      int configTimerValue = _remoteConfigService.getTimerInSeconds();
      remainingSeconds.value = configTimerValue;
      debugPrint(
          "Timer initialized from remote config: $configTimerValue seconds");
    } catch (e) {
      remainingSeconds.value = 500;
      debugPrint("Failed to initialize timer from remote config: $e");
    }
  }

  void startTimer() {
    // Get the latest timer value from remote config before starting
    // int configTimerValue = _remoteConfigService.getTimerInSeconds();
    // remainingSeconds.value = configTimerValue;

    // debugPrint("Timer is Starting with ${remainingSeconds.value} seconds...");
    if ((timer == null || !timer!.isActive) && remainingSeconds.value > 0) {
      timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (remainingSeconds.value > 0) {
          remainingSeconds.value--;
          debugPrint("Timer is going ${remainingSeconds.value}");
        } else {
          debugPrint("Timer is finished");
          canShowSubmitButtonForPPG.value = true;
          timer.cancel();
        }
      });
    }
  }

  Future<void> resetPpgTimer() async {
    debugPrint("DEBUG: resetPpgTimer called - timeCounter before reset: $timeCounter, ecgData length: ${ecgData.length}");

    bool isDisconnected =false;
    bool isPpgConnected = connectedDevices[BleDevice.ppg] != null;
    if (isPpgConnected){
      // Cancel notification subscription before disconnecting
      _notificationSubscription?.cancel();
      _notificationSubscription = null;
      debugPrint("DEBUG: Cancelled notification subscription");

      isDisconnected = await disconnect(type: BleDevice.ppg);
      debugPrint("DEBUG: Disconnection result: $isDisconnected");

      // Add a delay to allow the device to reset its internal buffers
      if (isDisconnected) {
        await Future.delayed(const Duration(milliseconds: 500));
        debugPrint("DEBUG: Waited for device buffer reset");
      }
    }
    else{
      isDisconnected =true;
    }
    if (isDisconnected) {
      heartRate.value = 0;
      spo2.value = 0;
      pi.value = 0.0;
      ecgData.clear(); // Use clear() instead of assigning empty list
      timeCounter = 0;
      _lastProcessedData = null; // Reset duplicate prevention
      timer = null;

      // Reset timer to the value from remote config
      int configTimerValue = _remoteConfigService.getTimerInSeconds();
      remainingSeconds.value = configTimerValue;

      canShowSubmitButtonForPPG.value = false;

      debugPrint("DEBUG: resetPpgTimer completed - timeCounter after reset: $timeCounter, ecgData length: ${ecgData.length}");
    }
  }

  void stopTimer() {
    timer?.cancel();
    timer = null;
    debugPrint("Timer is cancelled...");
  }

  // bool isDeviceConnecting(String deviceId) {
  //   return connectingDeviceIds.value.contains(deviceId);
  // }

  void startScan() async {
    isScanning.value = true;
    devices.value = [];
    devices.refresh();
    FlutterBluePlus.scanResults.listen((results) {
      for (ScanResult result in results) {
        bool hasTargetService = false;
        List<Guid> advertisedServices = result.advertisementData.serviceUuids;
        if (advertisedServices.isNotEmpty) {
          for (Guid serviceGuid in advertisedServices) {
            if (targetServiceUuids.contains(serviceGuid)) {
              hasTargetService = true;
              break;
            }
          }
        }

        if (hasTargetService ||
            (lastConnectedDeviceId != null &&
                result.device.remoteId.str == lastConnectedDeviceId)) {
          if (!devices.value.contains(result.device)) {
            devices.value.add(result.device);
            deviceAdvertisedServices[result.device.remoteId.str] =
                advertisedServices;
            devices.refresh();
          }
        }
      }
    });

    devices.refresh();

    // Set withServices parameter to filter scan results
    await FlutterBluePlus.startScan(
      timeout: const Duration(seconds: 15),
      withServices: targetServiceUuids,
      continuousUpdates: true,
      removeIfGone: const Duration(milliseconds: 50),
    );

    isScanning.value = false;
  }

  Future<bool> connect(BluetoothDevice device) async {
    BleDevice? deviceType;
    try {
      // Disconnect existing device of the same type if different
      attemptingReconnect.value = true;
      status.value = 'Connecting...';
      lastConnectedDeviceId = device.remoteId.str;

      // 1. Connect to the device first
      await device.connect(timeout: const Duration(seconds: 5));

      // 2. Discover services
      List<BluetoothService> services = await device.discoverServices();
      deviceType = _determineDeviceType(services);

      // Listen to connection state changes
      var subscription = device.connectionState.listen((state) async {
        if (state == BluetoothConnectionState.disconnected) {
          connectedDevices[deviceType!] = null;
          status.value = 'Disconnected';
          if(deviceType==BleDevice.ppg) {
            stopTimer();
          }
          Future.delayed(const Duration(seconds: 1), () {
            if (deviceType == BleDevice.ppg && timer != null) {
              _scheduleReconnect();
            }
          });
        } else if (state == BluetoothConnectionState.connected) {
          connectedDevices[deviceType!] = device;
          if (deviceType == BleDevice.ppg) {
            startTimer();
          }
          status.value = 'Connected';
        }
      });

      _connectionSubscriptions[device.remoteId.str] = subscription;

      // 3. Disconnect existing device of the same type if different
      BluetoothDevice? existingDevice = connectedDevices[deviceType];
      if (existingDevice != null && existingDevice != device) {
        await existingDevice.disconnect();
        _connectionSubscriptions.remove(existingDevice.remoteId.str)?.cancel();
        connectedDevices[deviceType] = null;
      }

      connectedDevices[deviceType] = device;
      status.value = 'Connected';
      _handleServices(services);

      return true; // Connection successful
    } catch (e) {
      debugPrint('Connection error: $e');
      status.value = 'Connection failed';
      if (deviceType == BleDevice.ppg) {
        stopTimer();
        _scheduleReconnect();
      }
      return false; // Connection failed
    } finally {
      attemptingReconnect.value = false;
    }
  }

  BleDevice _determineDeviceType(List<BluetoothService> services) {
    if (services.any((s) => s.uuid == oximeterService)) {
      return BleDevice.ppg;
    } else if (services.any((s) => s.uuid == tempService)) {
      return BleDevice.temp;
    } else if (services.any((s) => s.uuid == bpService)) {
      return BleDevice.bp;
    }
    throw Exception('Unknown device type');
  }

  Future<void> connectToSavedDevice(String remoteId) async {
    try {
      BluetoothDevice device =
      BluetoothDevice(remoteId: DeviceIdentifier(remoteId));
      await connect(device);
    } catch (e) {
      debugPrint('Error connecting to saved device: $e');
    }
  }

  void _handleServices(List<BluetoothService> services) {
    for (BluetoothService service in services) {
      if (service.uuid == oximeterService) {
        _setupOximeter(service);
      } else if (service.uuid == tempService) {
        _setupThermometer(service);
      } else if (service.uuid == bpService) {
        _setupBloodPressure(service);
      }
    }
  }

  Future<void> _setupOximeter(BluetoothService service) async {
    try {
      final characteristic = service.characteristics
          .firstWhere((c) => c.uuid == oximeterCharacteristic);

      // Cancel any existing subscription first
      _notificationSubscription?.cancel();
      _notificationSubscription = null;
      debugPrint("DEBUG: Cancelled existing subscription");

      // Disable notifications first, then re-enable to ensure clean state
      try {
        await characteristic.setNotifyValue(false);
        await Future.delayed(const Duration(milliseconds: 100));
        debugPrint("DEBUG: Disabled notifications temporarily");
      } catch (e) {
        debugPrint("DEBUG: Could not disable notifications: $e");
      }

      // Clear any buffered data by reading the characteristic once
      try {
        await characteristic.read();
        debugPrint("DEBUG: Cleared buffered data from characteristic");
      } catch (e) {
        debugPrint("DEBUG: Could not read characteristic to clear buffer: $e");
      }

      // Set up new subscription with duplicate prevention
      _notificationSubscription = characteristic.lastValueStream.distinct().listen((data) {
        String parsedData = _dataParser(data);
        _parseOximeterData(parsedData);
      });

      await characteristic.setNotifyValue(true);
      debugPrint("DEBUG: Oximeter setup completed with fresh subscription and duplicate prevention");
    } catch (e) {
      debugPrint("Error : $e");
    }
  }

  Future<void> _setupThermometer(BluetoothService service) async {
    try {
      BluetoothCharacteristic characteristic = service.characteristics
          .firstWhere((c) => c.uuid == tempCharacteristic);

      _notificationSubscription = characteristic.lastValueStream.listen((data) {
        String parsedData = _dataParser(data);
        _parseTempData(parsedData);
      });
      await characteristic.setNotifyValue(true);
    } catch (e) {
      debugPrint("Error : $e");
    }
  }

  Future<void> _setupBloodPressure(BluetoothService service) async {
    try {
      BluetoothCharacteristic characteristic = service.characteristics
          .firstWhere((c) => c.uuid == bpCharacteristic,
          orElse: () => throw Exception('BP characteristic not found'));

      _notificationSubscription = characteristic.lastValueStream.listen((data) {
        String parsedData = _dataParser(data);
        _parseBpData(parsedData);
      });
      await characteristic.setNotifyValue(true);
      List<int> value = await characteristic.read();
      _parseTempData(_dataParser(value));
    } catch (e) {
      debugPrint("Error : $e");
    }
  }

  String _dataParser(List<int> dataBytes) {
    debugPrint("Raw byte is  : $dataBytes");
    String result = String.fromCharCodes(dataBytes);
    if (result.contains(RegExp(r'[^\x20-\x7E]'))) {
      result = dataBytes
          .map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}')
          .join(', ');
    }
    return result;
  }

  void _parseOximeterData(String data) {
    // Prevent duplicate data processing
    if (_lastProcessedData == data) {
      debugPrint('DEBUG: Skipping duplicate data packet');
      return;
    }
    _lastProcessedData = data;

    List<int> bytes = [];

    if (data.contains('0x')) {
      try {
        bytes = data
            .split(', ')
            .map((hex) => int.parse(hex.trim().replaceAll('0x', ''), radix: 16))
            .toList();
        debugPrint('Parsed hex data: $bytes');
      } catch (e) {
        debugPrint('Error parsing hex string: $e');
        return;
      }
    } else {
      bytes = data.codeUnits;
      debugPrint('Parsed raw data: $bytes');
    }

    if (bytes.isEmpty) {
      debugPrint('No bytes to process.');
      return;
    }

    int firstByte = bytes[0];
    debugPrint('First byte: $firstByte');

    if (firstByte == 0x80 || firstByte == -128) {
      if (bytes.length < 11) {
        debugPrint('Insufficient bytes for pulse wave data.');
        return;
      }
      debugPrint('DEBUG: Processing pulse wave data - timeCounter before: $timeCounter, ecgData length before: ${ecgData.length}');
      for (int i = 1; i <= 10; i++) {
        if (i < bytes.length) {
          ecgData.add(LiveData(timeCounter++, bytes[i].toDouble()));
        }
        ecgData.refresh();
      }
      debugPrint('DEBUG: Processing pulse wave data - timeCounter after: $timeCounter, ecgData length after: ${ecgData.length}');
    } else if (firstByte == 0x81 || firstByte == -127) {
      if (bytes.length < 4) {
        debugPrint('Insufficient bytes for heart rate, SpO2, and PI data.');
        return;
      }

      if (bytes[1] != 255 && bytes[2] != 127) {
        heartRate.value = bytes[1];
        spo2.value = bytes[2];
      }

      double piValue = bytes[3] * 10 / 100;
      pi.value = double.parse(piValue.toStringAsFixed(2));

      debugPrint('Heart rate: ${heartRate.value}');
      debugPrint('SpO2: ${spo2.value}');
      debugPrint('Perfusion index: ${pi.value}');
    }
  }

  void _parseTempData(String data) {
    List<int> bytes = [];
    if (data.contains('0x')) {
      try {
        bytes = data
            .split(', ')
            .map((hex) => int.parse(hex.trim().replaceAll('0x', ''), radix: 16))
            .toList();
      } catch (e) {
        debugPrint('Error parsing hex string: $e');
        return;
      }
    } else {
      bytes = data.codeUnits;
    }

    if (bytes.length < 4) return;
    int tempRawValue = (bytes[2] << 8) | bytes[3];
    double tempCelsius = tempRawValue / 100.0;
    temperature.value =
        double.parse((tempCelsius * 9 / 5 + 32).toStringAsFixed(2));
  }

  /// Parse BP data
  void _parseBpData(String data) {
    List<int> bytes = [];

    if (data.contains('0x')) {
      try {
        bytes = data
            .split(', ')
            .map((hex) => int.parse(hex.trim().replaceAll('0x', ''), radix: 16))
            .toList();
        debugPrint('BP parsed hex data: $bytes');
      } catch (e) {
        debugPrint('Error parsing BP hex string: $e');
        return;
      }
    } else {
      bytes = data.codeUnits;
      debugPrint('BP parsed raw data: $bytes');
    }

    if (bytes.length < 4) {
      debugPrint('Insufficient BP bytes.');
      return;
    }
    systolicBP.value = bytes[1];
    diastolicBP.value = bytes[3];

    debugPrint(
        'Blood Pressure -> Systolic: ${systolicBP.value}, Diastolic: ${diastolicBP.value}');
  }

  Future<bool> disconnect({BleDevice? type}) async {
    try {
      if (type != null) {
        BluetoothDevice? device = connectedDevices[type];
        if (device != null) {
          // Disable notifications before disconnecting
          if (type == BleDevice.ppg) {
            await _disableOximeterNotifications(device);
          }

          await device.disconnect();
          connectedDevices[type] = null;
          debugPrint("DEBUG: Successfully disconnected ${type.description} device");
          return true;
        }
        return false;
      } else {
        bool disconnectedAny = false;
        for (var deviceType in [BleDevice.temp, BleDevice.bp]) {
          if (connectedDevices[deviceType] != null) {
            await connectedDevices[deviceType]?.disconnect();
            connectedDevices[deviceType] = null;
            disconnectedAny = true;
          }
        }

        return disconnectedAny;
      }
    } catch (e) {
      debugPrint("Error during disconnection: $e");
      return false;
    }
  }

  Future<void> _disableOximeterNotifications(BluetoothDevice device) async {
    try {
      List<BluetoothService> services = await device.discoverServices();
      for (BluetoothService service in services) {
        if (service.uuid == oximeterService) {
          final characteristic = service.characteristics
              .firstWhere((c) => c.uuid == oximeterCharacteristic);
          await characteristic.setNotifyValue(false);
          debugPrint("DEBUG: Disabled notifications for oximeter characteristic");
          break;
        }
      }
    } catch (e) {
      debugPrint("DEBUG: Could not disable oximeter notifications: $e");
    }
  }

  Future<bool> disconnectAndResetAll() async {
    try {
      bool disconnectedAny = false;
      for (var deviceType in [BleDevice.temp, BleDevice.bp, BleDevice.ppg]) {
        if (connectedDevices[deviceType] != null) {
          await connectedDevices[deviceType]?.disconnect();
          connectedDevices[deviceType] = null;
          disconnectedAny = true;
        }
      }

      // Reset all variables
      temperature.value = 0.0;
      heartRate.value = 0;
      spo2.value = 0;
      pi.value = 0.0;
      ecgData.clear();

      // Reset blood pressure variables
      systolicBP.value = 0;
      diastolicBP.value = 0;

      // Reset timer-related variables
      stopTimer();

      // Reset timer to the value from remote config
      int configTimerValue = _remoteConfigService.getTimerInSeconds();
      remainingSeconds.value = configTimerValue;

      canShowSubmitButtonForPPG.value = false;
      timeCounter = 0;
      _lastProcessedData = null; // Reset duplicate prevention

      // Reset connection-related variables
      status.value = 'Disconnected';
      lastConnectedDeviceId = null;
      attemptingReconnect.value = false;

      // Cancel any ongoing subscriptions
      _notificationSubscription?.cancel();
      _connectionStateSubscription?.cancel();
      _cancelReconnectTimer();

      return disconnectedAny;
    } catch (e) {
      debugPrint("Error during disconnection and reset: $e");
      return false;
    }
  }

  void _scheduleReconnect() {
    _cancelReconnectTimer();
    if (lastConnectedDeviceId != null) {
      status.value = 'Waiting to reconnect...';
      _reconnectTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        _attemptReconnect();
      });
    }
  }

  void _attemptReconnect() {
    if (attemptingReconnect.isTrue || connectedDevices[BleDevice.ppg] != null) {
      return;
    }
    status.value = 'Scanning for PPG device...';
    startScan(); // Scan will auto-connect PPG if found
  }

  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  // Method to manually trigger reconnection
  void reconnectLastDevice() {
    if (lastConnectedDeviceId != null) {
      _attemptReconnect();
    }
  }

  bool shouldShowDevice(
      {required BleDevice bleDevice, required String currentDeviceName}) {
    if (bleDevice == BleDevice.ppg) {
      if (currentDeviceName.toLowerCase().contains("oximeter")) {
        return true;
      }
    } else if (bleDevice == BleDevice.bp) {
      if (currentDeviceName.toLowerCase().contains("blood")) {
        return true;
      }
    } else if (bleDevice == BleDevice.temp) {
      if (currentDeviceName.toLowerCase().contains("thermometer")) {
        return true;
      }
    }
    return false;
  }

  bool canShowPopup(BleDevice bleDevice) {
    String routeName = Get.currentRoute;

    bool routeMatches = false;
    if (bleDevice == BleDevice.ppg && routeName == PpgReadingView.routeName) {
      routeMatches = true;
    } else if (bleDevice == BleDevice.temp &&
        routeName == TemperatureReadingView.routeName) {
      routeMatches = true;
    } else if (bleDevice == BleDevice.bp &&
        routeName == BloodPressureReadingView.routeName) {
      routeMatches = true;
    }

    bool isDeviceConnected = connectedDevices[bleDevice] != null;
    return routeMatches && !isDeviceConnected;
  }

  void showDeviceSelectionPopup(BleDevice bleDevice,
      {bool isCancelable = true}) {
    bool canShow = canShowPopup(bleDevice);
    if (canShow) {
      while (Get.isDialogOpen ?? false) {
        Get.back();
      }
      startScan();
      Get.dialog(
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Select ${bleDevice.description} Device'),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () => startScan(),
                tooltip: 'Refresh',
              ),
            ],
          ),
          content: Obx(() => SizedBox(
            width: 300,
            height: 300,
            child: devices.value.isEmpty
                ? const BuildEmptyScanList()
                : ListView.builder(
              shrinkWrap: true,
              itemCount: devices.value.length,
              itemBuilder: (ctx, i) {
                final device = devices[i];
                List<Guid> services =
                    deviceAdvertisedServices[device.remoteId.str] ??
                        [];
                String deviceName = device.platformName;
                if (services.contains(bpService)) {
                  deviceName = 'My BP (Blood Pressure)';
                }
                return shouldShowDevice(
                    bleDevice: bleDevice,
                    currentDeviceName: deviceName)
                    ? Card(
                  margin:
                  const EdgeInsets.symmetric(vertical: 4),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        const Icon(Icons.bluetooth,
                            color: Colors.blue),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            children: [
                              Text(
                                deviceName,
                                style: const TextStyle(
                                    fontWeight:
                                    FontWeight.bold),
                              ),
                              Text(
                                'ID: ${device.remoteId.str.substring(0, 8)}...',
                                style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        Obx(()=>
                            ElevatedButton(
                              onPressed: inConnectingPopUp.value
                                  ? null
                                  : () async {
                                try {
                                  inConnectingPopUp.value =true;

                                  await connect(device);
                                  if (bleDevice ==
                                      BleDevice.temp) {
                                    await prefs
                                        .setTempRemoteId(
                                        device.remoteId
                                            .str);
                                  } else if (bleDevice ==
                                      BleDevice.bp) {
                                    await prefs.setBpRemoteId(
                                        device.remoteId.str);
                                  } else if (bleDevice ==
                                      BleDevice.ppg) {
                                    await prefs
                                        .setPpgRemoteId(device
                                        .remoteId.str);
                                  }

                                  Get.back();
                                } catch (e) {
                                  Get.snackbar(
                                    'Connection Error',
                                    'Failed to connect to device. Please try again.',
                                    snackPosition:
                                    SnackPosition.BOTTOM,
                                  );
                                } finally {
                                  inConnectingPopUp.value =false;
                                }
                              },
                              child: Text(inConnectingPopUp.value
                                  ? 'Connecting...'
                                  : 'Connect'),
                            )),
                      ],
                    ),
                  ),
                )
                    : const BuildEmptyScanList();
              },
            ),
          )),
          actions: [
            if (isCancelable)
              TextButton(
                child: const Text('Cancel'),
                onPressed: () {
                  Get.back();
                },
              ),
          ],
        ),
        barrierDismissible: false,
      );
    }
  }
}